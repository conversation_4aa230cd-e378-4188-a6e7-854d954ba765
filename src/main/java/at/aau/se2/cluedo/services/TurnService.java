package at.aau.se2.cluedo.services;

import at.aau.se2.cluedo.models.gamemanager.GameManager;
import at.aau.se2.cluedo.models.gamemanager.GameState;
import at.aau.se2.cluedo.models.gameobjects.Player;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class TurnService {
    private static final Logger logger = LoggerFactory.getLogger(TurnService.class);

    @Autowired
    private GameService gameService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    // Track turn states for each lobby
    private final Map<String, TurnState> lobbyTurnStates = new HashMap<>();

    public enum TurnState {
        WAITING_FOR_PLAYERS,        // Lobby created, waiting for players to join
        WAITING_FOR_START,          // At least 3 players, waiting for host to start
        PLAYERS_TURN_ROLL_DICE,     // Current player needs to roll dice
        PLAYERS_TURN_MOVE,          // Current player needs to move
        PLAYERS_TURN_SUSPICION,     // Current player can make a suspicion (suggestion or accusation)
        PLAYERS_TURN_SOLVE,         // Current player can make a solve case attempt (optional, anytime during turn)
        PLAYERS_TURN_END,           // Current player's turn is ending
        PLAYER_HAS_WON              // Game finished, someone won
    }

    /**
     * Initialize lobby state when lobby is created
     */
    public void initializeLobbyState(String lobbyId) {
        lobbyTurnStates.put(lobbyId, TurnState.WAITING_FOR_PLAYERS);
        logger.info("Initialized lobby state for lobby: {}", lobbyId);
        notifyStateChange(lobbyId);
    }
    
    /**
     * Update state when enough players join (3+)
     */
    public void setWaitingForStart(String lobbyId) {
        if (getTurnState(lobbyId) == TurnState.WAITING_FOR_PLAYERS) {
            lobbyTurnStates.put(lobbyId, TurnState.WAITING_FOR_START);
            logger.info("Lobby {} now waiting for start", lobbyId);
            notifyStateChange(lobbyId);
        }
    }
    
    /**
     * Update state when players drop below 3
     */
    public void setWaitingForPlayers(String lobbyId) {
        if (getTurnState(lobbyId) == TurnState.WAITING_FOR_START) {
            lobbyTurnStates.put(lobbyId, TurnState.WAITING_FOR_PLAYERS);
            logger.info("Lobby {} now waiting for more players", lobbyId);
            notifyStateChange(lobbyId);
        }
    }
    
    /**
     * Initialize turn state when game starts
     */
    public void initializeTurnState(String lobbyId) {
        lobbyTurnStates.put(lobbyId, TurnState.PLAYERS_TURN_ROLL_DICE);
        logger.info("Game started in lobby: {}", lobbyId);
        notifyCurrentTurn(lobbyId);
    }

    /**
     * Check if it's a specific player's turn
     */
    public boolean isPlayerTurn(String lobbyId, String playerName) {
        GameManager game = gameService.getGame(lobbyId);
        if (game == null) {
            return false;
        }

        Player currentPlayer = game.getCurrentPlayer();
        return currentPlayer != null && currentPlayer.getName().equals(playerName);
    }

    /**
     * Get current turn state for a lobby
     */
    public TurnState getTurnState(String lobbyId) {
        return lobbyTurnStates.getOrDefault(lobbyId, TurnState.WAITING_FOR_PLAYERS);
    }

    /**
     * Process dice roll and advance to movement phase
     */
    public boolean processDiceRoll(String lobbyId, String playerName, int diceValue) {
        TurnState currentState = getTurnState(lobbyId);
        if (currentState != TurnState.PLAYERS_TURN_ROLL_DICE) {
            logger.warn("Invalid turn state for dice roll in lobby {}: {}", lobbyId, currentState);
            return false;
        }

        GameManager game = gameService.getGame(lobbyId);
        game.setDiceRollS(diceValue);

        // Advance to movement phase
        lobbyTurnStates.put(lobbyId, TurnState.PLAYERS_TURN_MOVE);

        // Notify all players about dice roll and state change
        messagingTemplate.convertAndSend("/topic/diceRolled/" + lobbyId,
                Map.of("player", playerName, "diceValue", diceValue, "turnState", TurnState.PLAYERS_TURN_MOVE));

        logger.info("Player {} rolled {} in lobby {}", playerName, diceValue, lobbyId);
        return true;
    }

    /**
     * Process player movement and determine next phase
     */
    public boolean processMovement(String lobbyId, String playerName) {
        TurnState currentState = getTurnState(lobbyId);
        if (currentState != TurnState.PLAYERS_TURN_MOVE) {
            logger.warn("Invalid turn state for movement in lobby {}: {}", lobbyId, currentState);
            return false;
        }

        GameManager game = gameService.getGame(lobbyId);
        Player currentPlayer = game.getCurrentPlayer();

        // Check if player is in a room after movement
        if (game.inRoom(currentPlayer)) {
            // Player can make a suspicion
            lobbyTurnStates.put(lobbyId, TurnState.PLAYERS_TURN_SUSPICION);
            messagingTemplate.convertAndSend("/topic/turnStateChanged/" + lobbyId,
                    Map.of("turnState", TurnState.PLAYERS_TURN_SUSPICION, "currentPlayer", currentPlayer.getName()));
        } else {
            // End turn if not in room
            endTurn(lobbyId);
        }

        return true;
    }

    /**
     * Process action (suggestion or accusation) and handle turn logic
     */
    public boolean processAction(String lobbyId, String playerName, String suspect, String weapon, String room, String actionType) {
        if (!isPlayerTurn(lobbyId, playerName)) {
            return false;
        }

        TurnState currentState = getTurnState(lobbyId);

        // For suggestions, must be in suspicion state and in a room
        if ("SUGGESTION".equals(actionType)) {
            if (currentState != TurnState.PLAYERS_TURN_SUSPICION) {
                logger.warn("Invalid turn state for suggestion in lobby {}: {}", lobbyId, currentState);
                return false;
            }
        }
        // For accusations, can be made during most turn phases
        else if ("ACCUSATION".equals(actionType)) {
            if (currentState == TurnState.PLAYERS_TURN_END || currentState == TurnState.PLAYER_HAS_WON) {
                logger.warn("Invalid turn state for accusation in lobby {}: {}", lobbyId, currentState);
                return false;
            }
        } else {
            logger.warn("Invalid action type: {}", actionType);
            return false;
        }

        GameManager game = gameService.getGame(lobbyId);
        Player currentPlayer = game.getCurrentPlayer();

        if ("SUGGESTION".equals(suspicionType)) {
            // Make the suggestion
            boolean suggestionDisproved = game.makeSuggestion(currentPlayer, suspect, weapon);

            // Notify all players about the suggestion
            messagingTemplate.convertAndSend("/topic/suspicionMade/" + lobbyId,
                    Map.of("player", playerName, "suspect", suspect, "weapon", weapon, "room", room,
                           "suspicionType", "SUGGESTION", "disproved", suggestionDisproved, "success", true));

            // End turn after suggestion
            endTurn(lobbyId);

            logger.info("Player {} made suggestion in lobby {}: {} with {}", playerName, lobbyId, suspect, weapon);
        } else if ("ACCUSATION".equals(suspicionType)) {
            // Check if accusation is correct
            boolean isCorrect = game.getCorrectSuspect().equals(suspect) &&
                    game.getCorrectWeapon().equals(weapon) &&
                    game.getCorrectRoom().equals(room);

            if (isCorrect) {
                // Player wins
                currentPlayer.setHasWon(true);
                lobbyTurnStates.put(lobbyId, TurnState.PLAYER_HAS_WON);

                // Notify all players about the win
                messagingTemplate.convertAndSend("/topic/suspicionMade/" + lobbyId,
                        Map.of("player", playerName, "suspect", suspect, "weapon", weapon, "room", room,
                               "suspicionType", "ACCUSATION", "correct", true, "gameWon", true, "success", true));

                logger.info("Player {} won the game in lobby {} with correct accusation!", playerName, lobbyId);
            } else {
                // Eliminate player
                game.eliminateCurrentPlayer();

                // Notify all players about the failed accusation
                messagingTemplate.convertAndSend("/topic/suspicionMade/" + lobbyId,
                        Map.of("player", playerName, "suspect", suspect, "weapon", weapon, "room", room,
                               "suspicionType", "ACCUSATION", "correct", false, "playerEliminated", true, "success", true));

                // End turn and move to next player
                endTurn(lobbyId);

                logger.info("Player {} eliminated in lobby {} with incorrect accusation", playerName, lobbyId);
            }
        }

        return true;
    }



    /**
     * End current turn and move to next player
     */
    public void endTurn(String lobbyId) {
        GameManager game = gameService.getGame(lobbyId);
        if (game == null || game.checkGameEnd()) {
            lobbyTurnStates.put(lobbyId, TurnState.PLAYER_HAS_WON);
            return;
        }

        lobbyTurnStates.put(lobbyId, TurnState.PLAYERS_TURN_END);

        nextTurn(lobbyId);
    }

    /**
     * Move to next player's turn
     */
    public void nextTurn(String lobbyId) {
        GameManager game = gameService.getGame(lobbyId);
        if (game == null || game.checkGameEnd()) {
            lobbyTurnStates.put(lobbyId, TurnState.PLAYER_HAS_WON);
            return;
        }

        game.nextTurn();
        lobbyTurnStates.put(lobbyId, TurnState.PLAYERS_TURN_ROLL_DICE);

        notifyCurrentTurn(lobbyId);

        logger.info("Advanced to next turn in lobby {}", lobbyId);
    }

    /**
     * Skip current player's turn (for eliminated players or timeouts)
     */
    public void skipTurn(String lobbyId, String reason) {
        logger.info("Skipping turn in lobby {} - reason: {}", lobbyId, reason);
        messagingTemplate.convertAndSend("/topic/turnSkipped/" + lobbyId,
                Map.of("reason", reason));
        nextTurn(lobbyId);
    }

    /**
     * Skip suspicion phase and end turn
     */
    public void skipSuspicion(String lobbyId, String playerName) {
        if (!isPlayerTurn(lobbyId, playerName)) {
            return;
        }

        TurnState currentState = getTurnState(lobbyId);
        if (currentState == TurnState.PLAYERS_TURN_SUSPICION) {
            endTurn(lobbyId);
            logger.info("Player {} skipped suspicion in lobby {}", playerName, lobbyId);
        }
    }



    /**
     * Get current player for a lobby
     */
    public Player getCurrentPlayer(String lobbyId) {
        GameManager game = gameService.getGame(lobbyId);
        return game != null ? game.getCurrentPlayer() : null;
    }

    /**
     * Check if player can make a suspicion (suggestion or accusation)
     */
    public boolean canMakeSuspicion(String lobbyId, String playerName, String suspicionType) {
        if (!isPlayerTurn(lobbyId, playerName)) {
            return false;
        }

        TurnState state = getTurnState(lobbyId);

        if ("SUGGESTION".equals(suspicionType)) {
            return state == TurnState.PLAYERS_TURN_SUSPICION;
        } else if ("ACCUSATION".equals(suspicionType)) {
            return state == TurnState.PLAYERS_TURN_ROLL_DICE ||
                   state == TurnState.PLAYERS_TURN_MOVE ||
                   state == TurnState.PLAYERS_TURN_SUSPICION;
        }

        return false;
    }



    /**
     * Force end game (for admin purposes)
     */
    public void forceEndGame(String lobbyId) {
        lobbyTurnStates.put(lobbyId, TurnState.PLAYER_HAS_WON);
        messagingTemplate.convertAndSend("/topic/gameEnded/" + lobbyId,
                Map.of("reason", "Game ended by admin"));
        logger.info("Game forcefully ended in lobby {}", lobbyId);
    }

    /**
     * Notify all players about current turn
     */
    private void notifyCurrentTurn(String lobbyId) {
        GameManager game = gameService.getGame(lobbyId);
        if (game == null) {
            return;
        }

        Player currentPlayer = game.getCurrentPlayer();
        TurnState turnState = getTurnState(lobbyId);

        messagingTemplate.convertAndSend("/topic/currentTurn/" + lobbyId,
                Map.of("currentPlayer", currentPlayer.getName(),
                        "turnState", turnState,
                        "playerIndex", game.getCurrentPlayerIndex()));
    }

    /**
     * Notify about state changes
     */
    private void notifyStateChange(String lobbyId) {
        TurnState turnState = getTurnState(lobbyId);
        
        messagingTemplate.convertAndSend("/topic/stateChanged/" + lobbyId, 
            Map.of("turnState", turnState, "lobbyId", lobbyId));
    }
}
