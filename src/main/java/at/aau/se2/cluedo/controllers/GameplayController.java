package at.aau.se2.cluedo.controllers;

import at.aau.se2.cluedo.dto.*;
import at.aau.se2.cluedo.models.gameboard.GameBoard;
import at.aau.se2.cluedo.models.gameobjects.Player;
import at.aau.se2.cluedo.models.gameobjects.SecretFile;
import at.aau.se2.cluedo.services.GameService;
import at.aau.se2.cluedo.services.LobbyService;
import at.aau.se2.cluedo.services.TurnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;


@Controller
public class GameplayController {

    private static final Logger logger = LoggerFactory.getLogger(GameplayController.class);

    @Autowired
    private LobbyService lobbyService;

    @Autowired
    private GameService gameService;

    @Autowired
    private TurnService turnService;



    @MessageMapping("/displayGameBoard/{lobbyId}")
    @SendTo("/topic/displayedGameBoard/{lobbyId}")
    public String displayGameBoard(@DestinationVariable String lobbyId, List<Player> players) {
        gameService.getGame(lobbyId).getGameBoard().displayGameBoard(players);
        return lobbyId;
    }
    @MessageMapping("/getGameBoard/{lobbyId}")
    @SendTo("/topic/gotGameBoard/{lobbyId}")
    public GameBoard getGameBoard(@DestinationVariable String lobbyId) {
        return gameService.getGame(lobbyId).getGameBoard();
    }

    /**
     * Handle action (suggestion or accusation) with turn validation
     */
    @MessageMapping("/makeAction/{lobbyId}")
    @SendTo("/topic/actionMade/{lobbyId}")
    public Map<String, Object> makeAction(@DestinationVariable String lobbyId, SuspicionRequest request) {
        try {
            // Validate turn
            if (!turnService.isPlayerTurn(lobbyId, request.getPlayerName())) {
                return Map.of(
                    "success", false,
                    "message", "It's not your turn",
                    "lobbyId", lobbyId
                );
            }

            if (!turnService.canMakeAction(lobbyId, request.getPlayerName(), request.getActionType())) {
                return Map.of(
                    "success", false,
                    "message", "Cannot make " + request.getActionType().toLowerCase() + " at this time",
                    "lobbyId", lobbyId
                );
            }

            boolean success = turnService.processAction(
                lobbyId,
                request.getPlayerName(),
                request.getSuspect(),
                request.getWeapon(),
                request.getRoom(),
                request.getActionType()
            );

            if (!success) {
                return Map.of(
                    "success", false,
                    "message", "Invalid " + request.getActionType().toLowerCase() + " attempt",
                    "lobbyId", lobbyId
                );
            }

            String actionVerb = "SUGGESTION".equals(request.getActionType()) ? "suggests" : "accuses";
            return Map.of(
                "success", true,
                "player", request.getPlayerName(),
                "suspect", request.getSuspect(),
                "weapon", request.getWeapon(),
                "room", request.getRoom(),
                "actionType", request.getActionType(),
                "message", request.getPlayerName() + " " + actionVerb + " " + request.getSuspect() + " with " + request.getWeapon() + " in " + request.getRoom(),
                "lobbyId", lobbyId
            );
        } catch (Exception e) {
            logger.error("Error processing action in lobby {}: {}", lobbyId, e.getMessage());
            return Map.of(
                "success", false,
                "message", "Error processing action",
                "lobbyId", lobbyId
            );
        }
    }
}
